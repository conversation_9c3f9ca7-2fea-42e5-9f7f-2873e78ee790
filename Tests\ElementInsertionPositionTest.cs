using System;
using System.Windows;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Tests
{
    /// <summary>
    /// 元素插入位置修正功能测试
    /// </summary>
    public class ElementInsertionPositionTest
    {
        /// <summary>
        /// 测试步骤到转换条件的连接点对齐
        /// </summary>
        public static void TestStepToTransitionAlignment()
        {
            Console.WriteLine("=== 测试步骤到转换条件的连接点对齐 ===");
            
            // 创建测试步骤
            var step = new SFCStepModel
            {
                Name = "TestStep",
                Position = new Point(100, 100),
                StepNumber = 1
            };
            
            // 模拟步骤的输出连接点位置
            // 步骤输出连接点：Position + (45 + 5, 117 + 5) = (150, 222)
            var stepOutputPoint = new Point(step.Position.X + 50, step.Position.Y + 122);
            Console.WriteLine($"步骤输出连接点: {stepOutputPoint}");
            
            // 转换条件的输入连接点偏移：(55.3 + 5, -5 + 5) = (60.3, 0)
            var transitionInputOffset = new Point(60.3, 0);
            Console.WriteLine($"转换条件输入连接点偏移: {transitionInputOffset}");
            
            // 计算转换条件的理想位置
            var transitionPosition = new Point(
                stepOutputPoint.X - transitionInputOffset.X,
                stepOutputPoint.Y - transitionInputOffset.Y
            );
            Console.WriteLine($"转换条件理想位置: {transitionPosition}");
            
            // 验证连接点重叠
            var transitionInputPoint = new Point(
                transitionPosition.X + transitionInputOffset.X,
                transitionPosition.Y + transitionInputOffset.Y
            );
            Console.WriteLine($"转换条件输入连接点: {transitionInputPoint}");
            
            var isAligned = Math.Abs(stepOutputPoint.X - transitionInputPoint.X) < 0.1 &&
                           Math.Abs(stepOutputPoint.Y - transitionInputPoint.Y) < 0.1;
            Console.WriteLine($"连接点对齐结果: {(isAligned ? "✅ 成功" : "❌ 失败")}");
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试转换条件到步骤的连接点对齐
        /// </summary>
        public static void TestTransitionToStepAlignment()
        {
            Console.WriteLine("=== 测试转换条件到步骤的连接点对齐 ===");
            
            // 创建测试转换条件
            var transition = new SFCTransitionModel
            {
                Name = "TestTransition",
                Position = new Point(200, 200)
            };
            
            // 模拟转换条件的输出连接点位置
            // 转换条件输出连接点：Position + (55.3 + 5, 25 + 5) = (260.3, 230)
            var transitionOutputPoint = new Point(transition.Position.X + 60.3, transition.Position.Y + 30);
            Console.WriteLine($"转换条件输出连接点: {transitionOutputPoint}");
            
            // 步骤的输入连接点偏移：(45 + 5, 1 + 5) = (50, 6)
            var stepInputOffset = new Point(50, 6);
            Console.WriteLine($"步骤输入连接点偏移: {stepInputOffset}");
            
            // 计算步骤的理想位置
            var stepPosition = new Point(
                transitionOutputPoint.X - stepInputOffset.X,
                transitionOutputPoint.Y - stepInputOffset.Y
            );
            Console.WriteLine($"步骤理想位置: {stepPosition}");
            
            // 验证连接点重叠
            var stepInputPoint = new Point(
                stepPosition.X + stepInputOffset.X,
                stepPosition.Y + stepInputOffset.Y
            );
            Console.WriteLine($"步骤输入连接点: {stepInputPoint}");
            
            var isAligned = Math.Abs(transitionOutputPoint.X - stepInputPoint.X) < 0.1 &&
                           Math.Abs(transitionOutputPoint.Y - stepInputPoint.Y) < 0.1;
            Console.WriteLine($"连接点对齐结果: {(isAligned ? "✅ 成功" : "❌ 失败")}");
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试选择分支的连接点对齐
        /// </summary>
        public static void TestSelectionBranchAlignment()
        {
            Console.WriteLine("=== 测试选择分支的连接点对齐 ===");
            
            // 创建测试步骤
            var step = new SFCStepModel
            {
                Name = "TestStep",
                Position = new Point(300, 300),
                StepNumber = 1
            };
            
            // 步骤输出连接点：Position + (45 + 5, 117 + 5) = (350, 422)
            var stepOutputPoint = new Point(step.Position.X + 50, step.Position.Y + 122);
            Console.WriteLine($"步骤输出连接点: {stepOutputPoint}");
            
            // 选择分支的输入连接点偏移（左上连接点）：(16 + 5, 2 + 5) = (21, 7)
            var branchInputOffset = new Point(21, 7);
            Console.WriteLine($"选择分支输入连接点偏移: {branchInputOffset}");
            
            // 计算选择分支的理想位置
            var branchPosition = new Point(
                stepOutputPoint.X - branchInputOffset.X,
                stepOutputPoint.Y - branchInputOffset.Y
            );
            Console.WriteLine($"选择分支理想位置: {branchPosition}");
            
            // 验证连接点重叠
            var branchInputPoint = new Point(
                branchPosition.X + branchInputOffset.X,
                branchPosition.Y + branchInputOffset.Y
            );
            Console.WriteLine($"选择分支输入连接点: {branchInputPoint}");
            
            var isAligned = Math.Abs(stepOutputPoint.X - branchInputPoint.X) < 0.1 &&
                           Math.Abs(stepOutputPoint.Y - branchInputPoint.Y) < 0.1;
            Console.WriteLine($"连接点对齐结果: {(isAligned ? "✅ 成功" : "❌ 失败")}");
            Console.WriteLine();
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始元素插入位置修正功能测试...\n");
            
            TestStepToTransitionAlignment();
            TestTransitionToStepAlignment();
            TestSelectionBranchAlignment();
            
            Console.WriteLine("所有测试完成！");
        }
    }
}