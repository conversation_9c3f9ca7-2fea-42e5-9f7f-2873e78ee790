  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: False
  连接ID: 4e1fa47d-b3c6-4724-b9f9-f8e4ac0268f4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: 4e1fa47d-b3c6-4724-b9f9-f8e4ac0268f4
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCTransitionViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 4e1fa47d-b3c6-4724-b9f9-f8e4ac0268f4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCTransitionViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 4e1fa47d-b3c6-4724-b9f9-f8e4ac0268f4
[UpdateConnectPointStates] 连接点状态更新完成: 924abad1-0ae7-4c4f-a5ad-d81ee9e88f8a[0] -> 9adddaa3-d0c2-4989-89cc-e53effa070bd[0]
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (250.5, 326.5), 距离: 4.5px, 重叠: True
[CreateConnectionPath] 连接 4e1fa47d-b3c6-4724-b9f9-f8e4ac0268f4 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (250.5, 326.5)
连接 4e1fa47d-b3c6-4724-b9f9-f8e4ac0268f4 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 4e1fa47d-b3c6-4724-b9f9-f8e4ac0268f4 的路径点
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCTransitionViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 190.2,326.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250.5,326.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCTransitionView] 拖动更新位置: 9adddaa3-d0c2-4989-89cc-e53effa070bd -> 190.2,326.5
[SFCCanvas] 位置变化: SFCTransitionViewModel.IsSelected
[GetActualConnectPointPosition] 转换条件连接点: 索引0, 位置250.5,356.5
[InsertEndAfterSelected] 动态计算结尾步骤位置: 连接点250.5,356.5, 步骤位置200.5,436.5
[SFCTerminatorView] ✅ 已绑定输入连接点适配器: fd612f3d-5dc6-41af-8467-797bad79fe28
[SFCCanvas] 为新创建的步骤添加位置变化监听: fd612f3d-5dc6-41af-8467-797bad79fe28
[AddConnection] 开始执行: 9adddaa3-d0c2-4989-89cc-e53effa070bd -> fd612f3d-5dc6-41af-8467-797bad79fe28
[AddConnection] 对象查找结果: sourceObject=SFCTransitionModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=190.2,326.5, 源Model位置=213,326.5
[AddConnection] 位置获取: 目标ViewModel位置=200.5,436.5, 目标Model位置=200.5,436.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 9adddaa3-d0c2-4989-89cc-e53effa070bd -> fd612f3d-5dc6-41af-8467-797bad79fe28
[AddConnection] 源位置: 190.2,326.5, 目标位置: 200.5,436.5
[AddConnection] 源对象类型: SFCTransitionModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 250.5,356.5, 目标连接点: 225.5,447.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 61efe747-fcc3-4178-a3de-fb750a84f03b 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250.5,356.5, 终点: 225.5,447.5
[AddConnection] 🔄 开始更新连接点状态: 9adddaa3-d0c2-4989-89cc-e53effa070bd -> fd612f3d-5dc6-41af-8467-797bad79fe28
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCTransitionViewModel
  连接点索引: 0
  是否输入: False
  连接ID: 61efe747-fcc3-4178-a3de-fb750a84f03b
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCTransitionViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: 61efe747-fcc3-4178-a3de-fb750a84f03b
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 61efe747-fcc3-4178-a3de-fb750a84f03b
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 61efe747-fcc3-4178-a3de-fb750a84f03b
[UpdateConnectPointStates] 连接点状态更新完成: 9adddaa3-d0c2-4989-89cc-e53effa070bd[0] -> fd612f3d-5dc6-41af-8467-797bad79fe28[0]
[CreateConnectionPath] 连接 61efe747-fcc3-4178-a3de-fb750a84f03b 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (250.5, 356.5), 终点: (225.5, 447.5), 距离: 94.4px
[贝塞尔曲线创建] 起点: (250.5, 356.5), 终点: (225.5, 447.5)
[贝塞尔曲线创建] Y差异: 91.0px, X差异: 25.0px, 控制点偏移: 36.4px
[贝塞尔曲线创建] PathPoints[0]: (250.5, 356.5), PathPoints[1]: (225.5, 447.5)
添加连接线: 61efe747-fcc3-4178-a3de-fb750a84f03b
[AddConnection] 延迟更新连接线 61efe747-fcc3-4178-a3de-fb750a84f03b 的路径点
[PathPoints集合变化] 连接 61efe747-fcc3-4178-a3de-fb750a84f03b 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 61efe747-fcc3-4178-a3de-fb750a84f03b 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 61efe747-fcc3-4178-a3de-fb750a84f03b 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCTransitionViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 190.2,326.5, 目标位置: 200.5,436.5
[AddConnection] 延迟更新后的路径点: 250.5,356.5 -> 225.5,447.5
