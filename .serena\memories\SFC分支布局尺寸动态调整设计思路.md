# PC_Control2 SFC分支动态布局系统设计文档

## 📋 文档概述

**文档版本**: v1.0  
**创建日期**: 2025-01-08  
**项目名称**: PC_Control2 工业PC图形化控制系统  
**功能模块**: SFC分支动态布局系统  
**设计目标**: 实现西门子博图Graph风格的多级嵌套分支动态布局功能  

---

## 🎯 需求背景

### **业务需求**
PC_Control2项目需要实现类似西门子博图Graph的SFC编辑功能，当前分支布局存在固定尺寸问题：
- 选择分支和并行分支尺寸固定，无法根据内容动态调整
- 嵌套分支时容易造成元素重叠
- 缺乏智能的空间布局算法

### **技术挑战**
- **多级嵌套复杂性**: 支持任意深度的分支嵌套结构
- **级联影响计算**: 子分支变化需要触发所有父级分支的尺寸重新计算
- **连接关系维护**: 布局变化后需要保持SFC图的连接拓扑完整性
- **性能优化**: 复杂布局计算不能影响用户体验

---

## 🏗️ 系统架构设计

### **1. 五层架构模型**

```
┌─────────────────────────────────────┐
│        交互层 (Interaction)         │  ← 用户操作、事件处理
├─────────────────────────────────────┤
│         UI层 (Presentation)        │  ← XAML视图、自定义控件
├─────────────────────────────────────┤
│       视图模型层 (ViewModel)        │  ← MVVM绑定、命令处理
├─────────────────────────────────────┤
│      业务逻辑层 (Business Logic)    │  ← 布局计算、嵌套检测
├─────────────────────────────────────┤
│        数据层 (Data Model)          │  ← SFCModel、SFCBranchModel
└─────────────────────────────────────┘
```

### **2. 核心组件设计**

#### **A. 分支层次管理器 (SFCBranchHierarchyManager)**
- **职责**: 管理分支的树形结构和父子关系
- **功能**: 
  - 添加/删除嵌套分支
  - 层次查询和遍历
  - 分支系统管理

#### **B. 级联布局引擎 (CascadingBranchLayoutEngine)**
- **职责**: 实现分支的级联布局计算
- **功能**:
  - 自底向上的尺寸计算
  - 自顶向下的位置更新
  - 水平线扩展长度计算

#### **C. 连接点同步器 (ConnectionPointSynchronizer)**
- **职责**: 同步分支布局变化后的连接点位置
- **功能**:
  - 连接点位置重新计算
  - 相关元素位置调整
  - 连接关系完整性维护

#### **D. UI更新协调器 (UIUpdateCoordinator)**
- **职责**: 协调布局计算和UI更新
- **功能**:
  - 异步布局计算
  - 批量UI更新
  - 动画过渡效果

---

## 📊 数据模型设计

### **1. 核心概念定义**

#### **分支层次结构**
```csharp
// 分支层次节点
public class BranchHierarchyNode
{
    public string BranchId { get; set; }
    public string? ParentBranchId { get; set; }
    public List<string> ChildBranchIds { get; set; } = new();
    public int NestingLevel { get; set; } = 0;
    public int IndexInParent { get; set; } = 0;
    public BranchSpatialInfo SpatialInfo { get; set; } = new();
}

// 分支空间信息
public class BranchSpatialInfo
{
    public Point RelativePosition { get; set; }        // 在父分支中的相对位置
    public Size OccupiedSpace { get; set; }            // 实际占用空间（包含子分支）
    public Size MinimumSpace { get; set; }             // 最小空间需求
    public Size CalculatedSize { get; set; }           // 计算得出的分支尺寸
    public Dictionary<int, Point> ConnectPointOffsets { get; set; } = new();
}
```

### **2. SFCBranchModel 扩展**
```csharp
public class SFCBranchModel : SFCElementModelBase
{
    // 层次结构属性
    public string? ParentBranchId { get; set; }
    public List<string> ChildBranchIds { get; set; } = new();
    public int NestingLevel { get; set; } = 0;
    
    // 空间布局属性
    public Size OccupiedSpace { get; set; } = new Size(120, 12);
    public Size CalculatedSize { get; set; } = new Size(120, 12);
    public Point RelativePosition { get; set; } = new Point(0, 0);
    
    // 布局状态属性
    public bool IsLayoutDirty { get; set; } = false;
    public double HorizontalLineExtension { get; set; } = 0;
}
```

---

## ⚙️ 核心算法设计

### **1. 级联尺寸调整算法**

#### **算法流程**
1. **触发检测**: 检测分支变化事件（添加、删除、移动子分支）
2. **影响范围确定**: 确定受影响的分支范围（祖先和后代分支）
3. **自底向上计算**: 从最深层子分支开始计算尺寸
4. **自顶向下更新**: 从根分支开始更新位置
5. **连接点同步**: 更新所有相关连接点位置

#### **水平线扩展计算**
```csharp
private double CalculateHorizontalLineExtension(int childBranchCount, int nestingLevel)
{
    const double BASE_EXTENSION_UNIT = 40.0;
    var levelMultiplier = 1.0 + (nestingLevel * 0.2);
    return childBranchCount * BASE_EXTENSION_UNIT * levelMultiplier;
}
```

### **2. 连接点位置同步算法**

#### **同步策略**
- **分支连接点更新**: 根据新的分支尺寸重新计算连接点位置
- **相关元素调整**: 调整连接到该分支的其他元素位置
- **连接关系维护**: 确保连接的拓扑关系完整性

#### **位置计算公式**
```
新元素位置 = 新分支连接点位置 - 元素连接点偏移量
```

---

## 🚀 性能优化策略

### **1. 增量更新机制**
- **延迟计算**: 使用定时器批量处理多个变化
- **优先级排序**: 按照影响范围和重要性排序更新
- **分组处理**: 将相关的更新分组批量处理

### **2. 布局缓存管理**
- **结果缓存**: 缓存布局计算结果避免重复计算
- **依赖追踪**: 建立缓存间的依赖关系
- **智能失效**: 只失效受影响的缓存项

### **3. UI响应性保证**
- **异步计算**: 复杂布局计算在后台线程进行
- **进度指示**: 提供计算进度反馈
- **动画过渡**: 使用平滑动画展示布局变化

---

## 🔧 UI更新机制

### **1. 数据绑定策略**
```xml
<!-- 动态尺寸绑定 -->
<Canvas Width="{Binding CalculatedSize.Width, FallbackValue=182}"
        Height="{Binding CalculatedSize.Height, FallbackValue=50}">
    
    <!-- 连接点动态位置 -->
    <controls:SFCConnectPoint 
        Canvas.Left="{Binding ConnectPointPositions[0].X}"
        Canvas.Top="{Binding ConnectPointPositions[0].Y}"/>
</Canvas>
```

### **2. 值转换器设计**
- **ConnectPointPositionConverter**: 计算连接点的动态位置
- **BranchSizeToInteractionAreaConverter**: 计算交互区域的大小
- **BranchSizeToLinePositionConverter**: 计算连接线的位置

### **3. XAML模板改造**
- **移除固定尺寸**: 将硬编码的宽度和高度改为动态绑定
- **连接点自适应**: 连接点位置使用转换器动态计算
- **交互区域调整**: 交互层随分支尺寸动态调整

---

## 🔗 系统集成方案

### **1. SFCCanvas 集成**
```csharp
public partial class SFCCanvas
{
    private CascadingBranchLayoutEngine _cascadingLayoutEngine;
    private UIUpdateCoordinator _updateCoordinator;
    
    // 扩展现有的元素添加方法
    public override void AddElement(SFCElementModelBase element)
    {
        base.AddElement(element);
        
        if (element is SFCBranchModel branch && !string.IsNullOrEmpty(branch.ParentBranchId))
        {
            // 触发级联布局更新
            _ = _incrementalUpdateManager.ScheduleIncrementalUpdate(
                branch.ParentBranchId, LayoutChangeType.ChildBranchAdded);
        }
    }
}
```

### **2. 连接管理系统集成**
- **连接验证增强**: 在连接验证时考虑布局影响
- **布局预测**: 预计算连接对布局的影响
- **自动调整**: 连接创建后自动触发相关布局更新

### **3. 验证系统集成**
- **布局验证**: 布局变化后重新验证SFC图正确性
- **冲突检测**: 检测布局变化可能引入的错误
- **规则扩展**: 添加布局相关的验证规则

---

## 📅 实施计划

### **Phase 1: 基础架构 (Week 1)**
- [ ] 设计和实现核心接口
- [ ] 扩展SFCBranchModel支持动态属性
- [ ] 创建基础的布局管理器框架
- [ ] 实现简单的缓存和失效机制

### **Phase 2: 算法实现 (Week 2)**
- [ ] 实现嵌套元素检测算法
- [ ] 实现选择分支和并行分支的尺寸计算算法
- [ ] 实现连接点位置计算逻辑
- [ ] 添加性能监控和日志记录

### **Phase 3: UI层改造 (Week 3)**
- [ ] 扩展SFCBranchViewModel支持动态属性
- [ ] 实现必要的值转换器
- [ ] 修改XAML模板支持动态绑定
- [ ] 测试UI响应性和性能

### **Phase 4: 集成优化 (Week 4)**
- [ ] 与SFCCanvas集成布局更新逻辑
- [ ] 实现批量更新和性能优化
- [ ] 添加错误处理和回退机制
- [ ] 完善单元测试和集成测试

### **Phase 5: 用户体验 (Week 5)**
- [ ] 添加用户配置选项
- [ ] 实现布局变化的动画过渡效果
- [ ] 添加布局计算的进度指示器
- [ ] 完善文档和用户指南

---

## ⚠️ 风险控制

### **1. 兼容性保证**
- **渐进式启用**: 通过配置开关控制是否启用动态布局
- **回退机制**: 布局计算失败时自动回退到固定布局
- **数据兼容**: 新增属性使用默认值，不影响现有数据

### **2. 性能监控**
- **计算时间监控**: 记录布局计算耗时，超时则使用缓存结果
- **内存使用监控**: 监控缓存大小，必要时清理过期缓存
- **UI响应性监控**: 确保布局计算不阻塞UI线程

### **3. 错误处理**
- **循环依赖检测**: 防止嵌套分支形成循环依赖
- **边界条件处理**: 处理极端尺寸和位置情况
- **异常恢复**: 布局计算异常时的优雅降级

---

## 🎯 预期效果

### **1. 功能完整性**
- ✅ **多级嵌套支持**: 支持任意深度的分支嵌套
- ✅ **智能尺寸调整**: 根据内容自动计算最优分支尺寸
- ✅ **级联更新**: 子分支变化自动触发父分支布局重新计算
- ✅ **连接同步**: 布局变化后连接点位置精确同步

### **2. 用户体验提升**
- ✅ **所见即所得**: 分支大小直观反映内容复杂度
- ✅ **防止重叠**: 自动避免嵌套元素的位置冲突
- ✅ **平滑过渡**: 布局变化使用动画过渡效果
- ✅ **响应性保证**: 复杂计算不影响UI响应性

### **3. 系统稳定性**
- ✅ **向后兼容**: 不破坏现有SFC功能
- ✅ **性能优化**: 高效的增量更新和缓存机制
- ✅ **错误处理**: 完善的异常处理和回退机制
- ✅ **可维护性**: 模块化设计，易于扩展和维护

---

## 📚 技术参考

### **相关技术标准**
- **IEC 61131-3**: 可编程控制器编程语言标准
- **西门子博图Graph**: SFC编程的工业标准实现
- **WPF MVVM模式**: Microsoft推荐的WPF应用架构

### **关键技术点**
- **树形数据结构**: 分支层次关系的表示和操作
- **级联计算算法**: 自底向上和自顶向下的布局计算
- **WPF数据绑定**: 动态UI更新的实现机制
- **异步编程**: 保证UI响应性的并发处理

---

**文档状态**: ✅ 设计完成，待实施  
**下一步行动**: 开始Phase 1基础架构的具体实现
